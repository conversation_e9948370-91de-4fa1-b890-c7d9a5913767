{"name": "www", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "dotenv -e .env -- react-router dev", "typecheck": "react-router typegen && tsc", "lint": "eslint ."}, "dependencies": {"@microsoft/fetch-event-source": "^2.0.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-slot": "^1.2.3", "@react-router/node": "^7.5.3", "@react-router/serve": "^7.5.3", "@tanstack/react-virtual": "^3.13.12", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dompurify": "^3.2.6", "eventsource-parser": "^3.0.3", "isbot": "^5.1.27", "katex": "^0.16.22", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "react-router": "^7.5.3", "react-shiki": "^0.7.2", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-math": "^6.0.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "zustand": "^5.0.6"}, "devDependencies": {"@react-router/dev": "^7.5.3", "@revisa/eslint-config": "*", "@revisa/typescript-config": "*", "@tailwindcss/vite": "^4.1.4", "@types/dompurify": "^3.0.5", "@types/katex": "^0.16.7", "@types/node": "^20", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "dotenv-cli": "^8.0.0", "prettier": "^3.4.2", "rollup-plugin-visualizer": "^6.0.3", "tailwindcss": "^4.1.4", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-tsconfig-paths": "^5.1.4"}}