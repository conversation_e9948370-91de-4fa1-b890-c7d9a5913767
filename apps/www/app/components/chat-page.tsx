// apps/www/app/components/chat-page.tsx
import { useRef, useEffect } from 'react';
import { useShallow } from 'zustand/react/shallow';
import { useParams } from 'react-router';

import { ChatHeader } from '~/components/chat/chat-header';
import { SmartMessageList } from '~/components/chat/message-list';
import { ChatInput } from '~/components/chat/chat-input';
import { useChatActions } from '~/hooks/useChatActions';
import { useChatForm } from '~/hooks/useChatForm';
import { useMediaQuery } from '~/hooks/useMediaQuery';
import { useChatStore } from '~/store/chatStore';
import { MainChatSkeleton } from '~/components/chat-skeleton';
import * as chatService from '~/services/chat-service';
import type { ChatMessage } from '~/types/chat';
import { ChatWelcome } from '~/components/chat/chat-welcome';

const EMPTY_MESSAGES: ChatMessage[] = [];

export function ChatPage() {
  const { sendMessage } = useChatActions();
  const { threadId: urlThreadId } = useParams<{ threadId?: string }>();
  const isSmallScreen = useMediaQuery('(max-width: 1023px)');

  const { setMessagesForThread, setCurrentThreadId, removeUnreadThread } =
    useChatStore.getState();

  // --- Smart Data Fetching & State Sync ---
  useEffect(() => {
    const currentId = urlThreadId || null;
    setCurrentThreadId(currentId);

    if (currentId) {
      removeUnreadThread(currentId);
    }

    if (
      currentId &&
      useChatStore.getState().messagesByThread[currentId] === undefined
    ) {
      const store = useChatStore.getState();
      chatService
        .fetchThreadTree(currentId)
        .then((threadTree) => {
          store.setThreadTree(currentId, threadTree);
          const messageMap = new Map<string, any>();
          threadTree.messages.forEach((msg: any) =>
            messageMap.set(msg.id, msg),
          );
          const messages = threadTree.currentPath
            .map((id: string) => {
              const msg = messageMap.get(id);
              if (!msg) return null;
              return {
                id: msg.id,
                content: msg.content,
                role: msg.role,
                createdAt: msg.createdAt,
                model: msg.model,
                threadId: msg.threadId,
                parentId: msg.parentId,
                siblingCount: msg.siblingCount,
                siblingPosition: msg.siblingPosition,
                deletedAt: msg.deletedAt,
              };
            })
            .filter(Boolean) as ChatMessage[];
          setMessagesForThread(currentId, messages);
        })
        .catch((err) => {
          console.error(`Thread tree fetch failed for ${currentId}:`, err);
          chatService
            .fetchMessages(currentId)
            .then((messages) => setMessagesForThread(currentId, messages))
            .catch((fetchErr) => {
              console.error(
                `Failed to fetch messages for thread ${currentId}:`,
                fetchErr,
              );
              setMessagesForThread(currentId, []); // On error, stop loading
            });
        });
    }
  }, [
    urlThreadId,
    setCurrentThreadId,
    setMessagesForThread,
    removeUnreadThread,
  ]);

  // --- Optimized Data Subscription ---
  const { messages, streamingResponse, isAiTyping, currentThreadId } =
    useChatStore(
      useShallow((state) => {
        const id = state.currentThreadId;
        return {
          messages: id ? state.messagesByThread[id] : EMPTY_MESSAGES,
          streamingResponse: id ? state.streamingResponses[id] : null,
          isAiTyping: id ? !!state.streamingResponses[id] : false,
          currentThreadId: id,
        };
      }),
    );

  const {
    inputValue,
    handleInputChange,
    handleEnterKeyPress,
    handleSendMessageClick,
    textareaRef,
  } = useChatForm({
    onSubmit: sendMessage,
    isAiTyping,
  });

  const isInitialLoadRef = useRef(true);
  useEffect(() => {
    isInitialLoadRef.current = true;
  }, [currentThreadId]);

  const isLoading = urlThreadId ? messages === undefined : false;
  const isNewAndEmpty = !urlThreadId && (!messages || messages.length === 0);

  return (
    <>
      <ChatHeader />

      {isLoading ? (
        <div className="flex-1 flex flex-col min-w-0">
          <MainChatSkeleton />
        </div>
      ) : isNewAndEmpty ? (
        <ChatWelcome />
      ) : (
        <SmartMessageList
          messages={messages || EMPTY_MESSAGES}
          streamingResponse={streamingResponse}
          isInitialLoadRef={isInitialLoadRef}
          isAiTyping={isAiTyping}
          isSmallScreen={isSmallScreen}
        />
      )}

      <ChatInput
        inputValue={inputValue}
        handleInputChange={handleInputChange}
        handleEnterKeyPress={handleEnterKeyPress}
        handleSendMessage={handleSendMessageClick}
        textareaRef={textareaRef}
        isSmallScreen={isSmallScreen}
        isAiTyping={isAiTyping}
      />
    </>
  );
}
