import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import rehypeRaw from 'rehype-raw';
import rehypeSanitize from 'rehype-sanitize';
import { useS<PERSON><PERSON><PERSON><PERSON><PERSON>er } from 'react-shiki/web';

interface MarkdownRendererProps {
  content: string;
}

// Safe sanitization schema - only allow table-related HTML elements
const tableSanitizeSchema = {
  tagNames: [
    // Standard markdown elements
    'p',
    'br',
    'strong',
    'em',
    'code',
    'pre',
    'a',
    'ul',
    'ol',
    'li',
    'h1',
    'h2',
    'h3',
    'h4',
    'h5',
    'h6',
    // Table elements
    'table',
    'thead',
    'tbody',
    'tfoot',
    'tr',
    'th',
    'td',
    'caption',
    'colgroup',
    'col',
  ],
  attributes: {
    // Allow basic attributes for tables
    table: ['class'],
    th: ['scope', 'colspan', 'rowspan', 'class'],
    td: ['colspan', 'rowspan', 'class'],
    a: ['href', 'title', 'target', 'rel'],
    '*': ['className'], // Allow className for styling
  },
  protocols: {
    href: ['http', 'https', 'mailto'],
  },
};

// Define a more accurate type for the props passed by `react-markdown` to custom components.
type CodeBlockProps = React.PropsWithChildren<{
  node?: any;
  className?: string;
  inline?: boolean;
  [key: string]: any;
}>;

/**
 * A dedicated component to render code elements.
 * This allows using hooks to add advanced interactivity like the custom
 * "select all" feature and syntax highlighting.
 */
const CodeBlock: React.FC<CodeBlockProps> = ({
  node,
  inline,
  className,
  children,
  ...props
}) => {
  const containerRef = React.useRef<HTMLDivElement>(null);
  const codeRef = React.useRef<HTMLElement | null>(null);

  // Effect to handle Ctrl+A (or Cmd+A) for selecting only the code block's content.
  React.useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleSelectAll = (event: KeyboardEvent) => {
      const isSelectAll = (event.ctrlKey || event.metaKey) && event.key === 'a';

      // Fire only if the user presses Ctrl+A and their focus is inside this code block.
      if (isSelectAll && container.contains(document.activeElement)) {
        event.preventDefault(); // Stop the browser from selecting the whole page.

        // Programmatically select the text inside the `code` element.
        if (codeRef.current) {
          const selection = window.getSelection();
          const range = document.createRange();
          range.selectNodeContents(codeRef.current);
          if (selection) {
            selection.removeAllRanges();
            selection.addRange(range);
          }
        }
      }
    };

    document.addEventListener('keydown', handleSelectAll);
    return () => {
      document.removeEventListener('keydown', handleSelectAll);
    };
  }, []); // Runs once on component mount.

  const match = /language-(\w+)/.exec(className || '');
  const language = match ? match[1] : undefined;
  const code = String(children).replace(/\n$/, '');

  // Use Shiki for syntax highlighting with a clean light theme
  const highlightedCode = useShikiHighlighter(code, language, 'github-light', {
    delay: 100, // Throttle for streaming performance
  });

  // Optimistic UI: show plain code while highlighted version loads
  const codeContent = highlightedCode ? (
    <div ref={codeRef as React.RefObject<HTMLDivElement>}>
      {highlightedCode}
    </div>
  ) : (
    <pre
      className="font-mono text-sm leading-relaxed"
      ref={codeRef as React.RefObject<HTMLPreElement>}
    >
      <code>{code}</code>
    </pre>
  );

  // Render inline `<code>` tags for non-fenced code.
  if (inline || !match) {
    return (
      <code
        className="bg-muted text-muted-foreground rounded-sm px-1.5 py-1 font-mono text-sm"
        {...props}
      >
        {children}
      </code>
    );
  }

  // Render the full, interactive code block for fenced code.
  return (
    <div
      ref={containerRef}
      className="code-block-container my-2 flex max-h-[60vh] flex-col overflow-hidden rounded-md bg-secondary text-secondary-foreground"
      // Add tabIndex to make the div focusable, which is required for the selection logic.
      tabIndex={-1}
    >
      <div className="z-10 flex flex-shrink-0 items-center justify-between border-b border-border bg-secondary px-4 py-2">
        <span className="text-xs capitalize text-muted-foreground">
          {language || 'text'}
        </span>
        <button
          onClick={() => navigator.clipboard.writeText(code)}
          className="rounded p-1 text-xs text-muted-foreground transition-colors hover:bg-accent hover:text-accent-foreground"
        >
          Copy
        </button>
      </div>
      <div className="code-block-scroller overflow-y-auto">
        <div className="p-4 shiki-wrapper">{codeContent}</div>
      </div>
    </div>
  );
};

/**
 * A component that renders markdown content, including LaTeX math expressions.
 * It leverages react-markdown with remark-math and rehype-katex.
 * This component is streaming-aware; incomplete markdown or LaTeX will be
 * rendered as plain text until it becomes valid.
 */
const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({ content }) => {
  return (
    <ReactMarkdown
      remarkPlugins={[remarkMath]}
      rehypePlugins={[
        rehypeRaw, // Allow HTML parsing
        [rehypeSanitize, tableSanitizeSchema], // Safely sanitize HTML
        rehypeKatex, // Keep math rendering
      ]}
      components={{
        p: ({ node, ...props }) => <p className="mb-2 last:mb-0" {...props} />,
        strong: ({ node, ...props }) => (
          <strong className="font-semibold" {...props} />
        ),
        a: ({ node, ...props }) => (
          <a
            className="text-primary hover:underline"
            target="_blank"
            rel="noopener noreferrer"
            {...props}
          />
        ),
        ul: ({ node, ...props }) => (
          <ul className="list-disc list-outside my-2 pl-6" {...props} />
        ),
        ol: ({ node, ...props }) => (
          <ol className="list-decimal list-outside my-2 pl-6" {...props} />
        ),
        li: ({ node, ...props }) => <li className="mb-1" {...props} />,
        // Table components with brutalist styling
        table: ({ node, ...props }) => (
          <div className="my-4 overflow-x-auto">
            <table
              className="w-full border-collapse border border-border"
              {...props}
            />
          </div>
        ),
        thead: ({ node, ...props }) => (
          <thead className="bg-secondary" {...props} />
        ),
        tbody: ({ node, ...props }) => <tbody {...props} />,
        tr: ({ node, ...props }) => (
          <tr className="border-b border-border hover:bg-muted/50" {...props} />
        ),
        th: ({ node, ...props }) => (
          <th
            className="border border-border px-4 py-2 text-left font-semibold text-secondary-foreground"
            {...props}
          />
        ),
        td: ({ node, ...props }) => (
          <td className="border border-border px-4 py-2" {...props} />
        ),
        // Use the enhanced CodeBlock component to render all code elements.
        code: CodeBlock,
      }}
    >
      {content}
    </ReactMarkdown>
  );
};

export default React.memo(MarkdownRenderer);
