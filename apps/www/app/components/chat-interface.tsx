// apps/www/app/components/chat-interface.tsx
import { useRef, useEffect } from 'react';
import { useShallow } from 'zustand/react/shallow';
import { useParams } from 'react-router';

import { ChatSidebar } from '~/components/chat/chat-sidebar';
import { Cha<PERSON>Header } from '~/components/chat/chat-header';
import { SmartMessageList } from '~/components/chat/message-list';
import { ChatInput } from '~/components/chat/chat-input';
import { useChatActions } from '~/hooks/useChatActions';
import { useChatForm } from '~/hooks/useChatForm';
import { useMediaQuery } from '~/hooks/useMediaQuery';
import { useChatStore } from '~/store/chatStore';
import { MainChatSkeleton } from '~/components/chat-skeleton';
import * as chatService from '~/services/chat-service';
import { useAuth } from '~/auth/useAuth';
import type { ChatMessage } from '~/types/chat';
import { useUIStore } from '~/store/uiStore';

const EMPTY_MESSAGES: [] = [];

export function ChatInterface() {
  const { sendMessage, createNewThread } = useChatActions();
  const { threadId: urlThreadId } = useParams<{ threadId?: string }>();
  const { isAuthenticated } = useAuth();
  const { isMobileSidebarOpen, setMobileSidebarOpen } = useUIStore();
  const isSmallScreen = useMediaQuery('(max-width: 1023px)');

  // Actions and static data can be pulled non-reactively or with stable selectors
  const {
    setMessagesForThread,
    setCurrentThreadId,
    setThreads,
    setThreadsStatus,
    removeUnreadThread,
  } = useChatStore.getState();
  const { threads, threadsStatus, unreadThreads } = useChatStore(
    useShallow((state) => ({
      threads: state.threads,
      threadsStatus: state.threadsStatus,
      unreadThreads: state.unreadThreads,
    })),
  );

  // --- Effect to fetch threads list ---
  useEffect(() => {
    // Only fetch if authenticated and threads haven't been loaded/are not loading
    if (isAuthenticated && threadsStatus === 'idle') {
      setThreadsStatus('loading');
      chatService
        .fetchThreads()
        .then((fetchedThreads) => {
          setThreads(fetchedThreads);
          setThreadsStatus('success');
        })
        .catch((err) => {
          console.error('Failed to fetch threads:', err);
          setThreadsStatus('error');
        });
    }
  }, [isAuthenticated, threadsStatus, setThreads, setThreadsStatus]);

  // --- Smart Data Fetching & State Sync ---
  // This effect synchronizes the URL, fetches data, and marks threads as read.
  useEffect(() => {
    const currentId = urlThreadId || null;
    setCurrentThreadId(currentId);

    // If we've navigated to a thread, mark it as read.
    if (currentId) {
      removeUnreadThread(currentId);
    }

    // Fetch messages only if a thread is selected and its messages are truly missing.
    if (
      currentId &&
      useChatStore.getState().messagesByThread[currentId] === undefined
    ) {
      // Set to an empty array immediately to mark as 'loading' and prevent re-fetches.
      setMessagesForThread(currentId, []);

      // Try to fetch thread tree first for better performance
      const store = useChatStore.getState();
      if (!store.threadTrees[currentId]) {
        chatService
          .fetchThreadTree(currentId)
          .then((threadTree) => {
            store.setThreadTree(currentId, threadTree);

            // Convert thread tree to messages for backward compatibility
            const messageMap = new Map<string, any>();
            threadTree.messages.forEach((msg: any) =>
              messageMap.set(msg.id, msg),
            );

            const messages = threadTree.currentPath
              .map((id: string) => {
                const msg = messageMap.get(id);
                if (!msg) return null;
                return {
                  id: msg.id,
                  content: msg.content,
                  role: msg.role,
                  createdAt: msg.createdAt,
                  model: msg.model,
                  threadId: msg.threadId,
                  parentId: msg.parentId,
                  siblingCount: msg.siblingCount,
                  siblingPosition: msg.siblingPosition,
                  deletedAt: msg.deletedAt,
                };
              })
              .filter(Boolean) as ChatMessage[];

            setMessagesForThread(currentId, messages);
          })
          .catch((err) => {
            console.error(`Thread tree fetch failed for ${currentId}:`, err);
            // Fallback to old method
            chatService
              .fetchMessages(currentId)
              .then((messages) => setMessagesForThread(currentId, messages))
              .catch((err) => {
                console.error(
                  `Failed to fetch messages for thread ${currentId}:`,
                  err,
                );
                // TODO: Handle UI error state, maybe remove the thread entry or show toast.
              });
          });
      } else {
        // We have thread tree, convert to messages
        const threadTree = store.threadTrees[currentId];
        const messageMap = new Map<string, any>();
        threadTree.messages.forEach((msg: any) => messageMap.set(msg.id, msg));

        const messages = threadTree.currentPath
          .map((id: string) => {
            const msg = messageMap.get(id);
            if (!msg) return null;
            return {
              id: msg.id,
              content: msg.content,
              role: msg.role,
              createdAt: msg.createdAt,
              model: msg.model,
              threadId: msg.threadId,
              parentId: msg.parentId,
              siblingCount: msg.siblingCount,
              siblingPosition: msg.siblingPosition,
              deletedAt: msg.deletedAt,
            };
          })
          .filter(Boolean) as ChatMessage[];

        setMessagesForThread(currentId, messages);
      }
    }
  }, [
    urlThreadId,
    setCurrentThreadId,
    setMessagesForThread,
    removeUnreadThread,
  ]);

  // --- Optimized Data Subscription ---
  const { messages, streamingResponse, isAiTyping, currentThreadId } =
    useChatStore(
      useShallow((state) => {
        const id = state.currentThreadId;
        const msgs = id ? state.messagesByThread[id] : EMPTY_MESSAGES;
        const stream = id ? state.streamingResponses[id] : null;
        return {
          messages: msgs === undefined ? EMPTY_MESSAGES : msgs, // Handle loading case
          streamingResponse: stream,
          isAiTyping: !!stream,
          currentThreadId: id,
        };
      }),
    );

  const {
    inputValue,
    handleInputChange,
    handleEnterKeyPress,
    handleSendMessageClick,
    textareaRef,
  } = useChatForm({
    onSubmit: sendMessage,
    isAiTyping,
  });

  const isInitialLoadRef = useRef(true);

  useEffect(() => {
    isInitialLoadRef.current = true;
  }, [currentThreadId]);

  const handleThreadSelect = () => {
    if (isSmallScreen) {
      setMobileSidebarOpen(false);
    }
  };

  const handleNewThreadClick = () => {
    createNewThread();
    if (isSmallScreen) {
      setMobileSidebarOpen(false);
    }
  };

  const isLoading = urlThreadId
    ? useChatStore.getState().messagesByThread[urlThreadId] === undefined
    : false;

  return (
    <div className="flex h-dvh bg-background overflow-hidden">
      <ChatSidebar
        threads={threads}
        currentThreadId={currentThreadId}
        onNewThread={handleNewThreadClick}
        unsentMessage={inputValue}
        unreadThreads={unreadThreads}
        onThreadSelect={handleThreadSelect}
      />
      {isSmallScreen && isMobileSidebarOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-30 lg:hidden"
          onClick={() => setMobileSidebarOpen(false)}
          aria-hidden="true"
        />
      )}
      {isLoading ? (
        <MainChatSkeleton />
      ) : (
        <div className="flex-1 flex flex-col min-w-0">
          <ChatHeader />
          <SmartMessageList
            messages={messages}
            streamingResponse={streamingResponse}
            isInitialLoadRef={isInitialLoadRef}
            isAiTyping={isAiTyping}
            isSmallScreen={isSmallScreen}
          />
          <ChatInput
            inputValue={inputValue}
            handleInputChange={handleInputChange}
            handleEnterKeyPress={handleEnterKeyPress}
            handleSendMessage={handleSendMessageClick}
            textareaRef={textareaRef}
            isSmallScreen={isSmallScreen}
            isAiTyping={isAiTyping}
          />
        </div>
      )}
    </div>
  );
}
