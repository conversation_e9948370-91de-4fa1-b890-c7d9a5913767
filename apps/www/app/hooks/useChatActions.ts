import { useCallback, useRef } from 'react';
import { useNavigate } from 'react-router';
import { toast } from 'sonner';
import DOMPurify from 'dompurify';

import { useAuth } from '~/auth/useAuth';
import * as chatService from '~/services/chat-service';
import { useChatStore } from '~/store/chatStore';
import { useChatConnection } from '~/hooks/useChatConnection';
import type { ChatMessage, ThreadTree } from '~/types/chat';

export function useChatActions() {
  const { isAuthenticated, login } = useAuth();
  const navigate = useNavigate();
  const { startStreamingResponse } = useChatConnection(navigate);
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  const {
    addThread,
    updateThread,
    setMessagesForThread,
    setCurrentThreadId,
    addMessageToThread,
    replaceMessageInThread,
    removeMessageFromThread,
    abortStreaming,
    setThreadTree,
    invalidateThreadTree,
  } = useChatStore.getState();

  const prefetchThread = useCallback(
    (threadId: string) => {
      const store = useChatStore.getState();

      // Prefer thread tree for better performance
      if (!store.threadTrees[threadId]) {
        console.log(`Prefetching thread tree for thread: ${threadId}`);
        chatService
          .fetchThreadTree(threadId)
          .then((threadTree) => {
            store.setThreadTree(threadId, threadTree);

            // Also update messagesByThread for backward compatibility
            const messageMap = new Map<string, any>();
            threadTree.messages.forEach((msg: any) =>
              messageMap.set(msg.id, msg),
            );

            const messages = threadTree.currentPath
              .map((id: string) => {
                const msg = messageMap.get(id);
                if (!msg) return null;
                return {
                  id: msg.id,
                  content: msg.content,
                  role: msg.role,
                  createdAt: msg.createdAt,
                  model: msg.model,
                  threadId: msg.threadId,
                  parentId: msg.parentId,
                  siblingCount: msg.siblingCount,
                  siblingPosition: msg.siblingPosition,
                  deletedAt: msg.deletedAt,
                };
              })
              .filter(Boolean) as ChatMessage[];

            setMessagesForThread(threadId, messages);
          })
          .catch((err) => {
            console.error(`Thread tree prefetch failed for ${threadId}`, err);
            // Fallback to old method
            if (!store.messagesByThread[threadId]) {
              console.log(
                `Fallback: Prefetching messages for thread: ${threadId}`,
              );
              chatService
                .fetchMessages(threadId)
                .then((msgs) => setMessagesForThread(threadId, msgs))
                .catch((err) =>
                  console.error(`Prefetch failed for ${threadId}`, err),
                );
            }
          });
      } else if (!store.messagesByThread[threadId]) {
        // We have thread tree but not messages, convert from tree
        const threadTree = store.threadTrees[threadId];
        const messageMap = new Map();
        threadTree.messages.forEach((msg) => messageMap.set(msg.id, msg));

        const messages = threadTree.currentPath
          .map((id: string) => {
            const msg = messageMap.get(id);
            if (!msg) return null;
            return {
              id: msg.id,
              content: msg.content,
              role: msg.role,
              createdAt: msg.createdAt,
              model: msg.model,
              threadId: msg.threadId,
              parentId: msg.parentId,
              siblingCount: msg.siblingCount,
              siblingPosition: msg.siblingPosition,
              deletedAt: msg.deletedAt,
            };
          })
          .filter(Boolean) as ChatMessage[];

        setMessagesForThread(threadId, messages);
      }
    },
    [setMessagesForThread],
  );

  const createNewThread = useCallback(() => {
    setCurrentThreadId(null);
    navigate('/');
  }, [setCurrentThreadId, navigate]);

  const sendMessage = useCallback(
    async (content: string) => {
      if (!content.trim()) return;
      if (!isAuthenticated) {
        toast.warning('Você precisa estar logado para enviar uma mensagem.', {
          description: 'Sua mensagem foi salva. Faça o login para continuar.',
          action: { label: 'Fazer Login', onClick: () => login(content) },
        });
        return;
      }
      const sanitizedInput = DOMPurify.sanitize(content);
      if (!sanitizedInput.trim()) return;
      const currentThreadId = useChatStore.getState().currentThreadId;
      const processSend = async (threadId: string, parentId: string | null) => {
        const tempUserMessageId = `temp-user-${Date.now()}`;
        addMessageToThread(threadId, {
          id: tempUserMessageId,
          content: sanitizedInput,
          role: 'USER',
          createdAt: new Date().toISOString(),
          threadId,
          parentId,
          siblingCount: 1,
          siblingPosition: 1,
        });
        try {
          const userMessage = await chatService.sendMessage(
            sanitizedInput,
            threadId,
            parentId,
            tempUserMessageId,
          );
          replaceMessageInThread(threadId, tempUserMessageId, userMessage);
          if (userMessage.threadTitle) {
            updateThread(threadId, userMessage.threadTitle);
          }
          startStreamingResponse(userMessage.id, userMessage.threadId);
        } catch {
          toast.error('Error sending message');
          abortStreaming(threadId);
          removeMessageFromThread(threadId, tempUserMessageId);
        }
      };
      if (currentThreadId) {
        const messages =
          useChatStore.getState().messagesByThread[currentThreadId] || [];
        const lastMessageId =
          messages.length > 0 ? messages[messages.length - 1].id : null;
        await processSend(currentThreadId, lastMessageId);
      } else {
        try {
          const newThread = await chatService.createThread(
            sanitizedInput.substring(0, 50),
          );
          addThread(newThread);
          navigate(`/c/${newThread.id}`);
          await processSend(newThread.id, null);
        } catch {
          toast.error('Failed to create new thread.');
        }
      }
    },
    [
      isAuthenticated,
      login,
      startStreamingResponse,
      addMessageToThread,
      replaceMessageInThread,
      removeMessageFromThread,
      abortStreaming,
      updateThread,
      addThread,
      navigate,
    ],
  );

  const handleTreeUpdate = (threadId: string, newTree: ThreadTree) => {
    setThreadTree(threadId, newTree);

    const messageMap = new Map();
    newTree.messages.forEach((msg) => messageMap.set(msg.id, msg));
    const newMessages = newTree.currentPath
      .map((id) => {
        const msg = messageMap.get(id);
        return {
          id: msg.id,
          content: msg.content,
          role: msg.role,
          createdAt: msg.createdAt,
          model: msg.model,
          threadId: msg.threadId,
          parentId: msg.parentId,
          siblingCount: msg.siblingCount,
          siblingPosition: msg.siblingPosition,
          deletedAt: msg.deletedAt,
        } as ChatMessage;
      })
      .filter(Boolean);

    setMessagesForThread(threadId, newMessages);
  };

  const saveEdit = useCallback(
    async (messageId: string, newContent: string) => {
      const currentThreadId = useChatStore.getState().currentThreadId;
      if (!newContent.trim() || !currentThreadId) return;

      const messages =
        useChatStore.getState().messagesByThread[currentThreadId] || [];
      const messageToEdit = messages.find((m) => m.id === messageId);
      if (!messageToEdit) {
        toast.error('Cannot edit message: original not found.');
        return;
      }

      try {
        const newTree = await chatService.editMessage(messageId, newContent);
        handleTreeUpdate(currentThreadId, newTree);

        if (messageToEdit.role === 'USER') {
          toast.success('Branch created. Generating new AI response...');
          const lastMessage =
            newTree.currentPath[newTree.currentPath.length - 1];
          startStreamingResponse(lastMessage, currentThreadId);
        } else {
          toast.success('Assistant message updated.');
        }
      } catch (error) {
        toast.error('Failed to update message.');
      }
    },
    [setThreadTree, setMessagesForThread, startStreamingResponse],
  );

  const regenerateResponse = useCallback(
    async (aiMessageId: string) => {
      const {
        currentThreadId,
        messagesByThread,
        setMessagesForThread,
        invalidateThreadTree,
      } = useChatStore.getState();

      if (!currentThreadId) return;
      const messages = messagesByThread[currentThreadId] || [];
      const aiMessage = messages.find((m) => m.id === aiMessageId);
      if (!aiMessage || !aiMessage.parentId) {
        toast.error('Cannot regenerate: context not found.');
        return;
      }

      const parentIndex = messages.findIndex(
        (m) => m.id === aiMessage.parentId,
      );
      const newHistory = messages.slice(0, parentIndex + 1);

      // Perform optimistic UI updates for better UX
      setMessagesForThread(currentThreadId, newHistory);
      invalidateThreadTree(currentThreadId); // Invalidate stale tree

      startStreamingResponse(aiMessage.parentId, currentThreadId, true);
    },
    [startStreamingResponse],
  );

  const deleteMessage = useCallback(
    async (messageId: string) => {
      const currentThreadId = useChatStore.getState().currentThreadId;
      if (!currentThreadId) return;
      try {
        const newTree = await chatService.deleteMessage(messageId);
        handleTreeUpdate(currentThreadId, newTree);
        toast.info('Created a new branch with the message deleted.');
      } catch {
        toast.error('Failed to delete message.');
      }
    },
    [setThreadTree, setMessagesForThread],
  );

  const navigateSibling = useCallback(
    (messageId: string, direction: 'next' | 'prev') => {
      const currentThreadId = useChatStore.getState().currentThreadId;
      if (!currentThreadId) return;

      const store = useChatStore.getState();

      // Perform the optimistic UI update using client-side logic.
      const clientSuccess = store.navigateSiblingClientSide(
        currentThreadId,
        messageId,
        direction,
      );

      if (clientSuccess) {
        // If there's a pending persistence call, cancel it.
        if (debounceTimerRef.current) {
          clearTimeout(debounceTimerRef.current);
        }

        // Debounce the persistence call. We'll only save the final
        // navigation state 2 seconds after the user stops clicking.
        debounceTimerRef.current = setTimeout(() => {
          chatService.persistNavigation(messageId, direction).catch((error) => {
            console.error('Failed to persist navigation change:', error);
            toast.error('Could not save navigation change to the server.');
          });
        }, 2000); // 2-second delay
      } else {
        toast.error(`No ${direction} version available.`);
      }
    },
    [], // No dependencies needed for this action
  );

  const copyMessage = useCallback((content: string) => {
    navigator.clipboard.writeText(content);
    toast.success('Copied to clipboard!');
  }, []);

  return {
    sendMessage,
    regenerateResponse,
    saveEdit,
    deleteMessage,
    navigateSibling,
    createNewThread,
    copyMessage,
    prefetchThread,
  };
}
