@import "tailwindcss";
@import "tw-animate-css";
@import "katex/dist/katex.min.css";

@custom-variant dark (&:is(.dark *));

@theme {
  --font-sans: "<PERSON><PERSON>st Sans", ui-sans-serif, system-ui, sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-mono: "Geist Mono", ui-monospace, monospace, "Apple Color Emoji",
    "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

/* Essential light theme enforcement */
* {
  color-scheme: light only;
}
@media (prefers-color-scheme: dark) {
  * {
    color-scheme: light !important;
  }
}

/* Improved mobile touch handling */
@media (max-width: 1023px) {
  .touch-manipulation {
    touch-action: manipulation;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

/* Brutalist light theme – softened steel + cooler whites */
:root {
  --radius: 0.125rem;

  /* Whites & slates – paper toned */
  --background: oklch(1 0 0);
  --foreground: oklch(0.18 0.01 250);
  --card: oklch(0.995 0.003 250);
  --card-foreground: var(--foreground);
  --popover: var(--card);
  --popover-foreground: var(--foreground);

  /* Steel-like primary */
  --primary: oklch(0.65 0.035 230);
  --primary-foreground: var(--background);

  --secondary: oklch(0.975 0.005 250);
  --secondary-foreground: oklch(0.3 0.01 250);

  --muted: oklch(0.985 0.004 250);
  --muted-foreground: oklch(0.55 0.01 250);

  --accent: oklch(0.96 0.006 250);
  --accent-foreground: var(--foreground);

  --destructive: oklch(0.55 0.12 20); /* bloodless red */

  --border: oklch(0.95 0.005 250);
  --input: oklch(0.95 0.005 250);
  --ring: var(--primary);

  --chart-1: var(--primary);
  --chart-2: oklch(0.75 0.02 250);
  --chart-3: oklch(0.65 0.015 250);
  --chart-4: oklch(0.55 0.02 250);
  --chart-5: oklch(0.45 0.02 250);

  /* Sidebar themed subtly different from bg */
  --sidebar: oklch(0.985 0.004 250);
  --sidebar-foreground: var(--foreground);
  --sidebar-primary: var(--primary);
  --sidebar-primary-foreground: var(--background);
  --sidebar-accent: var(--accent);
  --sidebar-accent-foreground: var(--foreground);
  --sidebar-border: var(--border);
  --sidebar-ring: var(--ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground tracking-normal;
  }

  /* Custom Scrollbar Styles using Theme Variables */
  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-track {
    background: var(--muted);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: var(--border);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: var(--muted-foreground);
  }

  /* Custom scrollbar for code blocks in markdown */
  .code-block-scroller::-webkit-scrollbar,
  .code-block-container pre::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .code-block-scroller::-webkit-scrollbar-track,
  .code-block-container pre::-webkit-scrollbar-track {
    background: var(--secondary);
    border-radius: 0;
  }

  .code-block-scroller::-webkit-scrollbar-thumb,
  .code-block-container pre::-webkit-scrollbar-thumb {
    background: var(--primary);
    border-radius: 0;
  }

  .code-block-scroller::-webkit-scrollbar-thumb:hover,
  .code-block-container pre::-webkit-scrollbar-thumb:hover {
    background: oklch(from var(--primary) calc(l + 0.05) c h);
  }

  /* Clean Brutalist Shiki theme - override github-light with our colors */
  .shiki-wrapper .shiki {
    background-color: transparent !important;
    color: var(--foreground) !important;
  }

  .shiki-wrapper .shiki code {
    background-color: transparent !important;
  }

  /* Brutalist color overrides for specific github-light token colors */
  /* Strings - darker steel */
  .shiki-wrapper .shiki span[style*="#032f62"] { color: oklch(0.45 0.02 250) !important; }

  /* Comments - muted and italic */
  .shiki-wrapper .shiki span[style*="#6a737d"] {
    color: var(--muted-foreground) !important;
    font-style: italic !important;
  }

  /* Keywords - primary steel, bold */
  .shiki-wrapper .shiki span[style*="#d73a49"] {
    color: var(--primary) !important;
    font-weight: 600 !important;
  }

  /* Functions - secondary foreground */
  .shiki-wrapper .shiki span[style*="#6f42c1"] {
    color: var(--secondary-foreground) !important;
    font-weight: 500 !important;
  }

  /* Numbers and constants - darker steel */
  .shiki-wrapper .shiki span[style*="#005cc5"] { color: oklch(0.55 0.02 250) !important; }
  .shiki-wrapper .shiki span[style*="#e36209"] { color: oklch(0.55 0.02 250) !important; }

  /* Brutalist HTML table styling */
  table {
    border-spacing: 0;
    border-collapse: collapse;
    /* Normal border */
    border: 1px solid var(--border);
  }

  table th {
    background-color: var(--secondary);
    font-weight: 600;
    border: 1px solid var(--border);
  }

  table td {
    border: 1px solid var(--border);
  }

  table tr:nth-child(even) {
    /* Use a more distinct color for alternating rows - darker than both muted and border */
    background-color: oklch(0.92 0.008 250);
  }

  /* When message is hovered (group hover), make table borders darker */
  .group:hover table,
  .group:hover table th,
  .group:hover table td {
    border-color: oklch(from var(--border) calc(l - 0.03) c h) !important;
  }
}